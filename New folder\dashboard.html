<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BuildPro Construction | Admin Panel</title>
    <meta name="description" content="BuildPro Construction Admin Panel - Manage your construction projects efficiently">
    <meta name="theme-color" content="#1e293b">
    
    <!-- PWA Meta Tags -->
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">
    <link rel="icon" type="image/png" href="icons/favicon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Modern Color Scheme */
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #3b82f6;
            --accent: #f97316;
            --light: #f8fafc;
            --dark: #1e293b;
            --gray: #64748b;
            --gray-light: #94a3b8;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --border: 1px solid rgba(0,0,0,0.05);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius: 12px;
            --radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background-color: #f1f5f9;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* Admin Panel Layout */
        .admin-container {
            display: flex;
            flex: 1;
            background: #f1f5f9;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            width: 280px;
            background: var(--dark);
            color: white;
            transition: var(--transition);
            padding: 24px 0;
            flex-shrink: 0;
            box-shadow: var(--shadow-lg);
            z-index: 10;
            border-right: var(--border);
            display: flex;
            flex-direction: column;
        }

        .admin-logo {
            padding: 0 24px 24px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .admin-logo h2 {
            font-size: 24px;
            font-weight: 700;
            color: white;
            letter-spacing: -0.5px;
        }

        .admin-logo span {
            color: var(--secondary);
        }

        .admin-menu {
            list-style: none;
            padding: 24px 0;
            flex: 1;
        }

        .admin-menu li {
            margin-bottom: 4px;
        }

        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-light);
            text-decoration: none;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            margin: 0 12px;
            position: relative;
        }

        .admin-menu a:hover, .admin-menu a.active {
            background: rgba(59, 130, 246, 0.1);
            color: white;
        }

        .admin-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: var(--secondary);
            border-radius: 0 3px 3px 0;
        }

        .admin-menu i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
            opacity: 0.9;
        }

        /* Main Content Area */
        .admin-main {
            flex-grow: 1;
            padding: 32px;
            background: #f1f5f9;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 20px 32px;
            border-radius: var(--radius-lg);
            margin-bottom: 32px;
            box-shadow: var(--shadow);
            border: var(--border);
        }

        .admin-title {
            font-size: 24px;
            color: var(--dark);
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .admin-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-bar {
            position: relative;
            margin-right: 16px;
        }

        .search-bar input {
            padding: 10px 16px 10px 40px;
            border: var(--border);
            border-radius: var(--radius);
            width: 240px;
            font-size: 14px;
            transition: var(--transition);
            background: var(--light);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--secondary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-bar i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
            font-size: 14px;
        }

        .notifications {
            position: relative;
        }

        .notifications-btn {
            padding: 10px;
            background: var(--light);
            border: var(--border);
            border-radius: var(--radius);
            color: var(--dark);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }

        .notifications-btn:hover {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary);
        }

        .notifications-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            border: 2px solid white;
        }

        .notifications-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 320px;
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            border: var(--border);
            margin-top: 8px;
            display: none;
            z-index: 100;
        }

        .notifications-dropdown.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        .notifications-header {
            padding: 16px;
            border-bottom: var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notifications-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
        }

        .notifications-list {
            max-height: 360px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 16px;
            border-bottom: var(--border);
            display: flex;
            gap: 12px;
            transition: var(--transition);
        }

        .notification-item:hover {
            background: var(--light);
        }

        .notification-item.unread {
            background: rgba(59, 130, 246, 0.05);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary);
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
        }

        .notification-content h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 4px;
        }

        .notification-content p {
            font-size: 13px;
            color: var(--gray);
            margin: 0;
        }

        .notification-time {
            font-size: 12px;
            color: var(--gray-light);
            margin-top: 4px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: var(--light);
            border: var(--border);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-profile:hover {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: inherit;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray);
        }

        .user-profile:hover .user-role {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Stats Cards */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: 32px;
            box-shadow: var(--shadow);
            border: var(--border);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--secondary);
            opacity: 0;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 16px;
            color: var(--gray);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 12px;
            line-height: 1;
        }

        .stat-card p {
            color: var(--gray);
            font-size: 14px;
            margin: 0;
        }

        /* Tables */
        .admin-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 24px;
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow);
            border: var(--border);
        }

        .admin-table th, .admin-table td {
            padding: 16px 24px;
            text-align: left;
            border-bottom: var(--border);
        }

        .admin-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--dark);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-table tr:last-child td {
            border-bottom: none;
        }

        .admin-table tr:hover {
            background: var(--light);
        }

        /* Action Buttons */
        .action-btn {
            padding: 8px 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            margin-right: 8px;
            font-size: 14px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .action-btn i {
            font-size: 14px;
        }

        .edit-btn {
            background: var(--secondary);
            color: white;
        }

        .edit-btn:hover {
            background: var(--primary);
            transform: translateY(-1px);
        }

        .delete-btn {
            background: var(--danger);
            color: white;
        }

        .delete-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        /* Badges */
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        /* Content Sections */
        .admin-content-section {
            display: none;
            background: white;
            border-radius: var(--radius-lg);
            padding: 32px;
            box-shadow: var(--shadow);
            border: var(--border);
            min-height: 400px;
            animation: fadeIn 0.3s ease;
        }

        .admin-content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-sidebar {
                width: 80px;
            }

            .admin-menu a {
                padding: 12px 0;
                margin: 0 8px;
                justify-content: center;
            }

            .admin-menu i {
                margin-right: 0;
                font-size: 18px;
            }

            .admin-main {
                padding: 20px;
            }

            .admin-header {
                padding: 16px 20px;
                margin-bottom: 24px;
            }

            .admin-title {
                font-size: 20px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .stat-card {
                padding: 24px;
            }
        }

        /* PWA Installation Prompt */
        .pwa-install-prompt {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 16px 24px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            display: none;
            align-items: center;
            gap: 16px;
            z-index: 1000;
            border: var(--border);
        }

        .pwa-install-prompt.show {
            display: flex;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Panel Section -->
    <div id="admin" class="admin-container">
        <div class="admin-sidebar">
            <div class="admin-logo">
                <h2>Build<span>Pro</span></h2>
                <p class="menu-text">Admin Panel</p>
            </div>
            <ul class="admin-menu">
                <li><a href="#" data-section="dashboard" class="active"><i class="fas fa-tachometer-alt"></i> <span class="menu-text">Dashboard</span></a></li>
                <li><a href="#" data-section="projects"><i class="fas fa-project-diagram"></i> <span class="menu-text">Projects</span></a></li>
                <li><a href="#" data-section="services"><i class="fas fa-concierge-bell"></i> <span class="menu-text">Services</span></a></li>
                <li><a href="#" data-section="media"><i class="fas fa-images"></i> <span class="menu-text">Media</span></a></li>
                <li><a href="#" data-section="messages"><i class="fas fa-envelope"></i> <span class="menu-text">Messages <span class="badge" style="background: var(--danger); margin-left: 5px;">1</span></span></a></li>
                <li><a href="#" data-section="pages"><i class="fas fa-file-alt"></i> <span class="menu-text">Pages</span></a></li>
                <li><a href="#" data-section="users"><i class="fas fa-users"></i> <span class="menu-text">Users</span></a></li>
                <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> <span class="menu-text">Settings</span></a></li>
                
                <li class="menu-divider"><span class="menu-text">Quick Actions</span></li>
                <li><a href="#" data-section="add-project"><i class="fas fa-plus-circle"></i> <span class="menu-text">Add Project</span></a></li>
                <li><a href="#" data-section="add-service"><i class="fas fa-plus-circle"></i> <span class="menu-text">Add Service</span></a></li>
                <li><a href="#" data-section="upload-media"><i class="fas fa-upload"></i> <span class="menu-text">Upload Media</span></a></li>
                
                <li class="menu-divider"><span class="menu-text">Website</span></li>
                <li><a href="#" data-section="view-website"><i class="fas fa-external-link-alt"></i> <span class="menu-text">View Website</span></a></li>
            </ul>
        </div>
        <div class="admin-main">
            <div class="admin-header">
                <h1 class="admin-title" id="admin-page-title">Dashboard</h1>
                <div class="admin-actions">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search...">
                    </div>
                    <div class="notifications">
                        <button class="notifications-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notifications-count">3</span>
                        </button>
                        <div class="notifications-dropdown">
                            <div class="notifications-header">
                                <h3>Notifications</h3>
                                <button class="btn" style="font-size: 12px;">Mark all as read</button>
                            </div>
                            <div class="notifications-list">
                                <div class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-project-diagram"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4>New Project Added</h4>
                                        <p>Metropolitan Office Complex has been added to your projects.</p>
                                        <div class="notification-time">2 minutes ago</div>
                                    </div>
                                </div>
                                <div class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4>New Message</h4>
                                        <p>You have received a new message from Sarah Williams.</p>
                                        <div class="notification-time">1 hour ago</div>
                                    </div>
                                </div>
                                <div class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4>Task Completed</h4>
                                        <p>Lakeside Luxury Residence project has been completed.</p>
                                        <div class="notification-time">3 hours ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-profile">
                        <div class="user-avatar">JD</div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-role">Administrator</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Sections -->
            <div id="dashboard-section" class="admin-content-section active">
                <div class="stats-cards">
                    <div class="stat-card">
                        <h3>Total Projects</h3>
                        <div class="number">56</div>
                        <p>12 ongoing</p>
                    </div>
                    <div class="stat-card">
                        <h3>New Messages</h3>
                        <div class="number">24</div>
                        <p>5 unread</p>
                    </div>
                    <div class="stat-card">
                        <h3>Services</h3>
                        <div class="number">8</div>
                        <p>All active</p>
                    </div>
                    <div class="stat-card">
                        <h3>Media Items</h3>
                        <div class="number">346</div>
                        <p>42 this month</p>
                    </div>
                </div>

                <h2>Recent Projects</h2>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Project Name</th>
                            <th>Category</th>
                            <th>Start Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Metropolitan Office Complex</td>
                            <td>Commercial</td>
                            <td>2023-05-15</td>
                            <td><span class="badge" style="background: #2ecc71;">Completed</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Lakeside Luxury Residence</td>
                            <td>Residential</td>
                            <td>2023-08-01</td>
                            <td><span class="badge" style="background: #3498db;">In Progress</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Advanced Manufacturing Facility</td>
                            <td>Industrial</td>
                            <td>2023-06-20</td>
                            <td><span class="badge" style="background: #3498db;">In Progress</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Downtown Retail Center</td>
                            <td>Commercial</td>
                            <td>2023-09-10</td>
                            <td><span class="badge" style="background: #f39c12;">Planning</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2 style="margin-top: 30px;">Recent Messages</h2>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Michael Johnson</td>
                            <td><EMAIL></td>
                            <td>Commercial Construction Inquiry</td>
                            <td>2023-10-05</td>
                            <td><span class="badge" style="background: #e74c3c;">New</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-eye"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Sarah Williams</td>
                            <td><EMAIL></td>
                            <td>Residential Renovation Quote</td>
                            <td>2023-10-03</td>
                            <td><span class="badge" style="background: #3498db;">Replied</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-eye"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Robert Chen</td>
                            <td><EMAIL></td>
                            <td>Industrial Facility Construction</td>
                            <td>2023-10-01</td>
                            <td><span class="badge" style="background: #2ecc71;">Completed</span></td>
                            <td>
                                <button class="action-btn edit-btn"><i class="fas fa-eye"></i></button>
                                <button class="action-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Placeholder Sections -->
            <div id="projects-section" class="admin-content-section">
                 <h2>Projects Management</h2>
                 <p>Content for managing projects goes here.</p>
                 <!-- Add specific project management content here -->
            </div>
            <div id="services-section" class="admin-content-section">
                 <h2>Services Management</h2>
                 <p>Content for managing services goes here.</p>
                 <!-- Add specific services management content here -->
            </div>
            <div id="media-section" class="admin-content-section">
                 <h2>Media Gallery</h2>
                 <p>Content for managing media goes here.</p>
                 <!-- Add specific media gallery content here -->
            </div>
            <div id="messages-section" class="admin-content-section">
                 <h2>Messages</h2>
                 <p>Content for viewing and responding to messages goes here.</p>
                 <!-- Add specific messages content here -->
            </div>
            <div id="analytics-section" class="admin-content-section">
                 <h2>Analytics and Reporting</h2>
                 <p>Content for viewing analytics goes here.</p>
                 <!-- Add specific analytics content here -->
            </div>
            <div id="users-section" class="admin-content-section">
                 <h2>User Management</h2>
                 <p>Content for managing users goes here.</p>
                 <!-- Add specific user management content here -->
            </div>
            <div id="settings-section" class="admin-content-section">
                 <h2>Settings</h2>
                 <p>Content for managing admin panel settings goes here.</p>
                 <!-- Add specific settings content here -->
            </div>

        </div>
    </div>

    <!-- PWA Install Prompt -->
    <div class="pwa-install-prompt" id="pwaInstallPrompt">
        <div>
            <h3>Install BuildPro Admin</h3>
            <p>Add to your home screen for quick access</p>
        </div>
        <button class="btn" id="installPWA">Install</button>
        <button class="btn" id="dismissPWA">Dismiss</button>
    </div>

    <script>
        // Admin sidebar navigation
        const adminMenuLinks = document.querySelectorAll('.admin-menu a');
        const adminContentSections = document.querySelectorAll('.admin-content-section');
        const adminPageTitle = document.getElementById('admin-page-title');

        function switchSection(sectionId) {
            // Hide all sections
            adminContentSections.forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            const activeSection = document.getElementById(sectionId);
            if (activeSection) {
                activeSection.classList.add('active');
            }

            // Update page title
            const activeLink = document.querySelector(`.admin-menu a[data-section="${sectionId.replace('-section', '')}"]`);
            if (activeLink) {
                const pageTitle = activeLink.querySelector('.menu-text').textContent;
                adminPageTitle.textContent = pageTitle;
            }
        }

        adminMenuLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all menu links
                adminMenuLinks.forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                this.classList.add('active');

                // Get section ID and switch to it
                const sectionId = this.getAttribute('data-section') + '-section';
                switchSection(sectionId);
            });
        });

        // Set initial active state for Dashboard on load
        document.addEventListener('DOMContentLoaded', () => {
            const initialLink = document.querySelector('.admin-menu a.active');
            if (initialLink) {
                const sectionId = initialLink.getAttribute('data-section') + '-section';
                switchSection(sectionId);
            } else {
                // If no initial active link, default to dashboard
                const dashboardLink = document.querySelector('.admin-menu a[data-section="dashboard"]');
                const dashboardSection = document.getElementById('dashboard-section');
                if(dashboardLink && dashboardSection) {
                    dashboardLink.classList.add('active');
                    switchSection('dashboard-section');
                }
            }
        });

        // PWA Installation
        let deferredPrompt;
        const pwaInstallPrompt = document.getElementById('pwaInstallPrompt');
        const installPWA = document.getElementById('installPWA');
        const dismissPWA = document.getElementById('dismissPWA');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            pwaInstallPrompt.classList.add('show');
        });

        installPWA.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                if (outcome === 'accepted') {
                    console.log('PWA installed successfully');
                }
                deferredPrompt = null;
                pwaInstallPrompt.classList.remove('show');
            }
        });

        dismissPWA.addEventListener('click', () => {
            pwaInstallPrompt.classList.remove('show');
        });

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(err => {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // Offline Support
        window.addEventListener('online', () => {
            console.log('Application is online');
        });

        window.addEventListener('offline', () => {
            console.log('Application is offline');
        });

        // Notifications Dropdown
        const notificationsBtn = document.querySelector('.notifications-btn');
        const notificationsDropdown = document.querySelector('.notifications-dropdown');

        notificationsBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notificationsDropdown.classList.toggle('show');
        });

        document.addEventListener('click', (e) => {
            if (!notificationsDropdown.contains(e.target) && !notificationsBtn.contains(e.target)) {
                notificationsDropdown.classList.remove('show');
            }
        });

        // Search Functionality
        const searchInput = document.querySelector('.search-bar input');
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            // Add your search logic here
            console.log('Searching for:', searchTerm);
        });
    </script>
</body>
</html>