/* Admin Panel Styles */
:root {
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --secondary-color: #1e293b;
    --sidebar-bg: #0f172a;
    --sidebar-hover: #1e293b;
    --sidebar-width: 263px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;

    /* Responsive breakpoints */
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;

    /* Fluid typography */
    --font-size-xs: clamp(0.7rem, 0.66rem + 0.2vw, 0.75rem);
    --font-size-sm: clamp(0.8rem, 0.75rem + 0.25vw, 0.875rem);
    --font-size-base: clamp(0.9rem, 0.85rem + 0.25vw, 1rem);
    --font-size-lg: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
    --font-size-xl: clamp(1.1rem, 1rem + 0.5vw, 1.25rem);
    --font-size-2xl: clamp(1.3rem, 1.2rem + 0.5vw, 1.5rem);
    --font-size-3xl: clamp(1.5rem, 1.4rem + 0.5vw, 1.875rem);
    --font-size-4xl: clamp(1.8rem, 1.6rem + 1vw, 2.25rem);

    /* Fluid spacing */
    --spacing-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
    --spacing-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
    --spacing-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
    --spacing-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
    --spacing-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
    --spacing-xxl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-bg);
    font-weight: 400;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-weight: 600;
    line-height: 1.4;
    color: var(--text-primary);
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 60px 0 0;
    box-shadow: var(--shadow-lg);
    background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0c1425 100%);
    width: var(--sidebar-width);
    border-right: 1px solid #1e293b;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 60px);
    padding: 1.5rem 0;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #cbd5e1;
    padding: 0.875rem 1.5rem;
    border-radius: 0;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    margin: 0.125rem 0.75rem;
    border-radius: var(--radius-md);
    position: relative;
}

.sidebar .nav-link:hover {
    color: #ffffff;
    background-color: var(--sidebar-hover);
    transform: translateX(2px);
}

.sidebar .nav-link.active {
    color: #ffffff;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    box-shadow: var(--shadow-md);
}

.sidebar .nav-link.active::before {
    content: '';
    position: absolute;
    left: -0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    width: 18px;
    font-size: 1rem;
}

.sidebar-heading {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-muted);
    margin: 1.5rem 1.5rem 0.5rem;
}

.navbar-brand {
    padding: 1rem 1.5rem;
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-weight: 700;
    font-size: 1.125rem;
}

.navbar .form-control {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--card-bg);
    transition: all 0.2s ease-in-out;
}

.navbar .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.text-xs {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.btn {
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all 0.2s ease-in-out;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #4f46e5 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.card-body {
    padding: 1.5rem;
}

.card.shadow {
    box-shadow: var(--shadow-md);
}

main {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    min-height: 100vh;
}

.dropdown-toggle::after {
    margin-left: 0.5rem;
}

.message-item:last-child {
    border-bottom: none !important;
}

/* Stats Cards Styling */
.stats-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stats-card .h5 {
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-weight: 700;
    font-size: 1.75rem;
    margin-bottom: 0;
}

/* Table Styling */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: var(--light-bg);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    color: var(--text-secondary);
    padding: 1rem 1.25rem;
}

.table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--light-bg);
}

/* Badge Styling */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-sm);
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: #f0fdf4;
    color: #166534;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

/* Modern Navbar Styling */
.modern-navbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1030;
    height: 70px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 2rem 0 0;
    max-width: 100%;
}

/* Left Section */
.navbar-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease-in-out;
    flex-direction: column;
    gap: 3px;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
}

.mobile-menu-toggle:hover {
    background-color: var(--light-bg);
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    border-radius: 2px;
    transition: all 0.3s ease-in-out;
}

.mobile-menu-toggle.collapsed .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.collapsed .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.collapsed .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease-in-out;
}

.navbar-brand:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
}

.brand-text {
    line-height: 1.2;
}

.brand-name {
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: 0;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Center Section - Search */
.navbar-center {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-sm);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    color: var(--text-muted);
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

.search-input {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-shortcut {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-left: 0.75rem;
    color: var(--text-muted);
    font-size: 0.75rem;
}

.search-shortcut kbd {
    background: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-size: 0.7rem;
    font-family: inherit;
    color: var(--text-secondary);
}

/* Right Section */
.navbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quick-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    border: none;
    background: var(--card-bg);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid white;
}

/* User Profile Section */
.user-profile-dropdown {
    position: relative;
}

.user-profile-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    min-width: 200px;
}

.user-profile-toggle:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.user-avatar-container {
    position: relative;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.user-avatar-placeholder {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    border: 2px solid var(--border-color);
}

.user-status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border: 2px solid white;
    border-radius: 50%;
}

.user-info {
    flex: 1;
    text-align: left;
    line-height: 1.3;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 0.125rem;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.dropdown-arrow {
    color: var(--text-muted);
    font-size: 0.75rem;
    transition: transform 0.2s ease-in-out;
}

.user-profile-toggle[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

/* Modern Dropdown Menu */
.modern-dropdown-menu {
    min-width: 360px;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    padding: 0;
    overflow: hidden;
    margin-top: 0.5rem;
    background: var(--card-bg);
}

.dropdown-user-header {
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.user-header-avatar {
    position: relative;
}

.header-avatar-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.header-avatar-placeholder {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.header-avatar-initials {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.user-status-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: var(--success-color);
    border: 3px solid white;
    border-radius: 50%;
}

.user-header-info {
    flex: 1;
}

.user-header-name {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: white;
}

.user-header-email {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.role-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dropdown-quick-stats {
    padding: 1.5rem;
    background: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.stats-refresh-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--card-bg);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    font-size: 0.75rem;
}

.stats-refresh-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: rotate(180deg);
}

.stats-refresh-btn.loading {
    animation: spin 1s linear infinite;
}

.stats-grid {
    display: flex;
    gap: 1rem;
}

.quick-stat-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.stat-info {
    flex: 1;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    transition: all 0.3s ease-in-out;
}

.stat-growth {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    line-height: 1;
}

.stat-growth.positive {
    background: #dcfce7;
    color: #166534;
}

.stat-growth.negative {
    background: #fef2f2;
    color: #dc2626;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.125rem;
}

.stat-sublabel {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-weight: 400;
}

.projects-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.messages-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.quick-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-stat-item:hover .stat-number {
    color: var(--primary-color);
}

/* Loading and Animation States */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -8px, 0);
    }

    70% {
        transform: translate3d(0, -4px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

.animate__pulse {
    animation-name: pulse;
}

.animate__bounce {
    animation-name: bounce;
}

.animate__fadeInUp {
    animation-name: fadeInUp;
}

/* Stat number loading state */
.stat-number .fa-spinner {
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.stat-number .fa-exclamation-triangle {
    color: var(--warning-color);
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Enhanced hover effects for quick stats */
.quick-stat-item {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.quick-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.quick-stat-item:hover::before {
    left: 100%;
}

.quick-stat-item:active {
    transform: translateY(-1px) scale(0.98);
}

.dropdown-menu-items {
    padding: 1rem 0;
}

.modern-dropdown-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease-in-out;
    border: none;
    background: none;
    width: 100%;
}

.modern-dropdown-item:hover {
    background: var(--light-bg);
    color: var(--text-primary);
    text-decoration: none;
    transform: translateX(4px);
}

.modern-dropdown-item.logout-item {
    color: var(--danger-color);
}

.modern-dropdown-item.logout-item:hover {
    background: #fef2f2;
    color: var(--danger-color);
}

.item-icon {
    width: 40px;
    height: 40px;
    background: var(--light-bg);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s ease-in-out;
}

.modern-dropdown-item:hover .item-icon {
    background: var(--primary-color);
    color: white;
}

.modern-dropdown-item.logout-item .item-icon {
    background: #fef2f2;
    color: var(--danger-color);
}

.modern-dropdown-item.logout-item:hover .item-icon {
    background: var(--danger-color);
    color: white;
}

.item-content {
    flex: 1;
}

.item-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
}

.item-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.3;
}

.item-arrow {
    color: var(--text-muted);
    font-size: 0.75rem;
    transition: all 0.2s ease-in-out;
}

.modern-dropdown-item:hover .item-arrow {
    color: var(--primary-color);
    transform: translateX(2px);
}

.dropdown-footer {
    padding: 1rem 1.5rem;
    background: var(--light-bg);
    border-top: 1px solid var(--border-color);
}

.last-login-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 1199.98px) {
    .navbar-center {
        display: none !important;
    }

    .navbar-container {
        padding: 0 1rem;
    }

    .user-profile-toggle {
        min-width: auto;
        padding: 0.5rem;
    }

    .modern-dropdown-menu {
        min-width: 320px;
    }
}

@media (max-width: 991.98px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .quick-actions {
        gap: 0.25rem;
    }

    .quick-action-btn {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 767.98px) {
    .navbar-container {
        padding: 0 0.75rem;
    }

    .navbar-left {
        gap: 1rem;
    }

    .quick-actions {
        display: none;
    }

    .modern-dropdown-menu {
        min-width: 280px;
    }

    .dropdown-quick-stats {
        flex-direction: column;
        gap: 0.75rem;
    }
}

.dropdown-menu {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
    min-width: 200px;
    z-index: 1050;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: var(--light-bg);
    color: var(--text-primary);
}

.dropdown-item.text-danger:hover {
    background-color: #fef2f2;
    color: var(--danger-color);
}

.dropdown-toggle::after {
    margin-left: 0.5rem;
    vertical-align: middle;
}

.dropdown-toggle:focus {
    box-shadow: none;
}

/* Enhanced User Avatar Styling */
.user-avatar {
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease-in-out;
}

.user-avatar:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.user-avatar-placeholder {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease-in-out;
}

.user-avatar-placeholder:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.user-avatar-placeholder-large {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    font-weight: 600;
    border: 2px solid var(--primary-color);
}

.user-avatar-large {
    border: 2px solid var(--primary-color);
}

.avatar-initials {
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.avatar-initials-large {
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* User Info Styling */
.user-info {
    line-height: 1.2;
}

.user-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: white;
    margin-bottom: 0;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* User Dropdown Toggle */
.user-dropdown-toggle {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease-in-out;
}

.user-dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Enhanced Dropdown Menu */
.user-dropdown-menu {
    min-width: 320px;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 0;
    overflow: hidden;
}

.user-dropdown-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-bottom: none;
    margin-bottom: 0;
}

.user-dropdown-info {
    flex: 1;
}

.user-dropdown-name {
    font-size: 1rem;
    color: white;
    margin-bottom: 0.25rem;
}

.user-dropdown-email {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.user-dropdown-role .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white;
}

.user-last-login {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 0.75rem;
}

.user-last-login small {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Enhanced Dropdown Items */
.dropdown-item {
    padding: 1rem 1.5rem;
    border: none;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: var(--light-bg);
    transform: translateX(2px);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    margin-right: 0.75rem;
}

.dropdown-item-content {
    flex: 1;
}

.dropdown-item-title {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
}

.dropdown-item-desc {
    font-size: 0.75rem;
    line-height: 1.2;
}

.dropdown-item.text-danger:hover {
    background-color: #fef2f2;
    color: var(--danger-color);
}

.dropdown-item.text-danger .dropdown-item-desc {
    color: rgba(239, 68, 68, 0.7) !important;
}

/* Notification Badge */
.nav-link .badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
    min-width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Page Header */
.page-header {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.page-header h1 {
    margin-bottom: 0;
    font-size: 1.75rem;
    font-weight: 700;
}

/* Performance Optimizations */

/* Image optimization and lazy loading */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

img[data-src] {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

img[data-src].loaded {
    opacity: 1;
}

/* Optimized image containers */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-md);
}

.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

/* Optimized table rendering */
.table-responsive {
    will-change: scroll-position;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--light-bg);
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: var(--light-bg);
    border-radius: var(--radius-sm);
}

.table-responsive::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--radius-sm);
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Efficient animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Enhanced Responsive Design */

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding-top: 0;
    }

    main {
        margin-left: 0;
        padding: 0.75rem;
    }

    .navbar-brand {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .stats-card {
        margin-bottom: 0.75rem;
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .page-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .page-header h1 {
        font-size: 1.5rem;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding-top: 0;
    }

    main {
        margin-left: 0;
        padding: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .sidebar {
        width: var(--sidebar-width);
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        padding-top: 60px;
    }

    main {
        margin-left: var(--sidebar-width);
        padding: 1.5rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    main {
        padding: 2rem;
    }

    .stats-card {
        padding: 2rem;
    }

    .card-body {
        padding: 2rem;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    main {
        padding: 2.5rem;
    }

    .page-header {
        padding: 2rem 2.5rem;
    }
}

/* Responsive Utility Classes */

/* Display utilities */
.d-xs-none {
    display: none !important;
}

.d-xs-inline {
    display: inline !important;
}

.d-xs-inline-block {
    display: inline-block !important;
}

.d-xs-block {
    display: block !important;
}

.d-xs-flex {
    display: flex !important;
}

.d-xs-grid {
    display: grid !important;
}

@media (min-width: 576px) {
    .d-sm-none {
        display: none !important;
    }

    .d-sm-inline {
        display: inline !important;
    }

    .d-sm-inline-block {
        display: inline-block !important;
    }

    .d-sm-block {
        display: block !important;
    }

    .d-sm-flex {
        display: flex !important;
    }

    .d-sm-grid {
        display: grid !important;
    }
}

@media (min-width: 768px) {
    .d-md-none {
        display: none !important;
    }

    .d-md-inline {
        display: inline !important;
    }

    .d-md-inline-block {
        display: inline-block !important;
    }

    .d-md-block {
        display: block !important;
    }

    .d-md-flex {
        display: flex !important;
    }

    .d-md-grid {
        display: grid !important;
    }
}

@media (min-width: 992px) {
    .d-lg-none {
        display: none !important;
    }

    .d-lg-inline {
        display: inline !important;
    }

    .d-lg-inline-block {
        display: inline-block !important;
    }

    .d-lg-block {
        display: block !important;
    }

    .d-lg-flex {
        display: flex !important;
    }

    .d-lg-grid {
        display: grid !important;
    }
}

@media (min-width: 1200px) {
    .d-xl-none {
        display: none !important;
    }

    .d-xl-inline {
        display: inline !important;
    }

    .d-xl-inline-block {
        display: inline-block !important;
    }

    .d-xl-block {
        display: block !important;
    }

    .d-xl-flex {
        display: flex !important;
    }

    .d-xl-grid {
        display: grid !important;
    }
}

/* Text utilities */
.text-xs-left {
    text-align: left !important;
}

.text-xs-center {
    text-align: center !important;
}

.text-xs-right {
    text-align: right !important;
}

@media (min-width: 576px) {
    .text-sm-left {
        text-align: left !important;
    }

    .text-sm-center {
        text-align: center !important;
    }

    .text-sm-right {
        text-align: right !important;
    }
}

@media (min-width: 768px) {
    .text-md-left {
        text-align: left !important;
    }

    .text-md-center {
        text-align: center !important;
    }

    .text-md-right {
        text-align: right !important;
    }
}

/* Spacing utilities */
.p-responsive {
    padding: var(--spacing-md);
}

.m-responsive {
    margin: var(--spacing-md);
}

.gap-responsive {
    gap: var(--spacing-md);
}

/* Container utilities */
.container-fluid-responsive {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .container-fluid-responsive {
        max-width: 540px;
        padding-right: var(--spacing-lg);
        padding-left: var(--spacing-lg);
    }
}

@media (min-width: 768px) {
    .container-fluid-responsive {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container-fluid-responsive {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container-fluid-responsive {
        max-width: 1140px;
    }
}

/* Performance optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-scroll {
    will-change: scroll-position;
}

.will-change-opacity {
    will-change: opacity;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}