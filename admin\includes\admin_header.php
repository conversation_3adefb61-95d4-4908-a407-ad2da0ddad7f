<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Admin Panel - Flori Construction</title>
    
    <!-- Performance Optimized CSS Loading -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Critical CSS - Bootstrap (minified) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Font Awesome (optimized loading) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS (minified) -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" crossorigin="anonymous">

    <!-- Google Fonts (optimized with font-display: swap) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet" crossorigin="anonymous">
    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <!-- Modern Top Navigation -->
    <nav class="modern-navbar">
        <div class="navbar-container">
            <!-- Left Section: Brand & Mobile Toggle -->
            <div class="navbar-left">
                <button class="mobile-menu-toggle d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>

                <a class="navbar-brand" href="dashboard.php">
                    <div class="brand-icon">
                        <i class="fas fa-hard-hat"></i>
                    </div>
                    <div class="brand-text d-none d-md-block">
                        <div class="brand-name">Flori Construction</div>
                        <div class="brand-subtitle">Admin Panel</div>
                    </div>
                </a>
            </div>

            <!-- Center Section: Search & Quick Actions -->
            <div class="navbar-center d-none d-lg-flex">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search projects, messages..." id="globalSearch">
                        <div class="search-shortcut">
                            <kbd>Ctrl</kbd> + <kbd>K</kbd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Section: Notifications & User -->
            <div class="navbar-right">
                <!-- Quick Actions -->
                <div class="quick-actions d-none d-md-flex">
                    <a href="project-add.php" class="quick-action-btn" title="Add New Project">
                        <i class="fas fa-plus"></i>
                    </a>
                    <a href="messages.php" class="quick-action-btn position-relative" title="Messages">
                        <i class="fas fa-envelope"></i>
                        <span class="notification-badge" id="message-badge" style="display: none;">0</span>
                    </a>
                    <button class="quick-action-btn" title="Notifications" id="notificationToggle">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                    </button>
                </div>

                <!-- User Profile Dropdown -->
                <div class="user-profile-dropdown">
                    <button class="user-profile-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="userDropdown">
                        <?php
                        // Get user's full information
                        require_once '../config/database.php';
                        $database = new Database();
                        $conn = $database->getConnection();
                        $stmt = $conn->prepare("SELECT first_name, last_name, email, avatar, role, last_login FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                        // Generate initials for avatar fallback
                        $initials = '';
                        if ($userInfo && ($userInfo['first_name'] || $userInfo['last_name'])) {
                            $initials = strtoupper(substr($userInfo['first_name'], 0, 1) . substr($userInfo['last_name'], 0, 1));
                            $displayName = trim($userInfo['first_name'] . ' ' . $userInfo['last_name']);
                        } else {
                            $initials = strtoupper(substr($_SESSION['username'], 0, 2));
                            $displayName = $_SESSION['username'];
                        }

                        // Display avatar or initials
                        if ($userInfo && !empty($userInfo['avatar']) && file_exists('../assets/images/avatars/' . $userInfo['avatar'])) {
                            echo '<div class="user-avatar-container">';
                            echo '<img src="../assets/images/avatars/' . htmlspecialchars($userInfo['avatar']) . '" alt="User Avatar" class="user-avatar">';
                            echo '<div class="user-status-indicator"></div>';
                            echo '</div>';
                        } else {
                            echo '<div class="user-avatar-container">';
                            echo '<div class="user-avatar-placeholder">';
                            echo '<span class="avatar-initials">' . htmlspecialchars($initials) . '</span>';
                            echo '</div>';
                            echo '<div class="user-status-indicator"></div>';
                            echo '</div>';
                        }

                        // Display user info
                        echo '<div class="user-info d-none d-xl-block">';
                        echo '<div class="user-name">' . htmlspecialchars($displayName) . '</div>';
                        echo '<div class="user-role">' . htmlspecialchars(ucfirst($userInfo['role'] ?? 'Admin')) . '</div>';
                        echo '</div>';

                        echo '<div class="dropdown-arrow">';
                        echo '<i class="fas fa-chevron-down"></i>';
                        echo '</div>';
                        ?>
                    </button>
                    <div class="modern-dropdown-menu dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <!-- User Info Header -->
                        <div class="dropdown-user-header">
                            <div class="user-header-avatar">
                                <?php
                                if ($userInfo && !empty($userInfo['avatar']) && file_exists('../assets/images/avatars/' . $userInfo['avatar'])) {
                                    echo '<img src="../assets/images/avatars/' . htmlspecialchars($userInfo['avatar']) . '" alt="User Avatar" class="header-avatar-img">';
                                } else {
                                    echo '<div class="header-avatar-placeholder">';
                                    echo '<span class="header-avatar-initials">' . htmlspecialchars($initials) . '</span>';
                                    echo '</div>';
                                }
                                ?>
                                <div class="user-status-dot"></div>
                            </div>
                            <div class="user-header-info">
                                <div class="user-header-name"><?php echo htmlspecialchars($displayName); ?></div>
                                <div class="user-header-email"><?php echo htmlspecialchars($userInfo['email'] ?? 'No email'); ?></div>
                                <div class="user-header-role">
                                    <span class="role-badge"><?php echo htmlspecialchars(ucfirst($userInfo['role'] ?? 'Admin')); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="dropdown-quick-stats">
                            <div class="stats-header">
                                <h6 class="stats-title">Quick Overview</h6>
                                <button class="stats-refresh-btn" id="refreshStats" title="Refresh Statistics">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>

                            <div class="stats-grid">
                                <div class="quick-stat-item" data-target="projects.php">
                                    <div class="stat-icon projects-icon">
                                        <i class="fas fa-project-diagram"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-header">
                                            <div class="stat-number" id="user-projects-count">-</div>
                                            <div class="stat-growth" id="projects-growth" style="display: none;"></div>
                                        </div>
                                        <div class="stat-label">Total Projects</div>
                                        <div class="stat-sublabel">Active & Completed</div>
                                    </div>
                                </div>
                                <div class="quick-stat-item" data-target="messages.php">
                                    <div class="stat-icon messages-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-header">
                                            <div class="stat-number" id="user-messages-count">-</div>
                                            <div class="stat-growth" id="messages-growth" style="display: none;"></div>
                                        </div>
                                        <div class="stat-label">Total Messages</div>
                                        <div class="stat-sublabel">Contact Inquiries</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="dropdown-menu-items">
                            <a href="profile.php" class="modern-dropdown-item">
                                <div class="item-icon">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-title">My Profile</div>
                                    <div class="item-subtitle">Account settings & preferences</div>
                                </div>
                                <div class="item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>

                            <a href="settings.php" class="modern-dropdown-item">
                                <div class="item-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-title">Settings</div>
                                    <div class="item-subtitle">System configuration</div>
                                </div>
                                <div class="item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>

                            <a href="messages.php" class="modern-dropdown-item">
                                <div class="item-icon">
                                    <i class="fas fa-inbox"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-title">Messages</div>
                                    <div class="item-subtitle">Contact form submissions</div>
                                </div>
                                <div class="item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>

                            <div class="dropdown-divider"></div>

                            <a href="logout.php" class="modern-dropdown-item logout-item" onclick="return confirm('Are you sure you want to logout?')">
                                <div class="item-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-title">Sign Out</div>
                                    <div class="item-subtitle">Logout from your account</div>
                                </div>
                                <div class="item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </div>

                        <!-- Last Login Info -->
                        <?php if ($userInfo && $userInfo['last_login']): ?>
                        <div class="dropdown-footer">
                            <div class="last-login-info">
                                <i class="fas fa-clock"></i>
                                Last login: <?php echo date('M j, Y g:i A', strtotime($userInfo['last_login'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar backdrop for mobile -->
    <div class="sidebar-backdrop" onclick="closeSidebar()"></div>
